{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build:prod": "nuxi build  --dotenv .env.production  --prd", "build:test": "nuxi build --dotenv .env.test --mode test", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@kangc/v-md-editor": "^2.3.18", "@microsoft/fetch-event-source": "^2.0.1", "@nuxtjs/dotenv": "^1.4.2", "@nuxtjs/i18n": "^9.5.2", "@nuxtjs/sitemap": "^7.2.10", "@nuxtjs/tailwindcss": "^6.13.2", "axios": "^1.8.4", "element-plus": "^2.9.7", "highlight.js": "^11.11.1", "js-cookie": "^3.0.5", "marked": "^16.0.0", "marked-highlight": "^2.2.2", "nuxt": "^3.16.2", "sass-embedded": "^1.86.3", "vant": "^4.9.18", "vconsole": "^3.15.1", "vue": "^3.5.13", "vue-i18n": "^11.1.3", "vue-qr": "^4.0.9", "vue-router": "^4.5.0"}}