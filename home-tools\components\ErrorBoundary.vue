<template>
  <div>
    <div v-if="hasError" class="render-error">
      <h3>渲染错误</h3>
      <p>内容渲染时发生错误，请检查内容格式。</p>
      <details v-if="errorDetails">
        <summary>错误详情</summary>
        <pre>{{ errorDetails }}</pre>
      </details>
    </div>
    <div v-else>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
const hasError = ref(false)
const errorDetails = ref('')

// Vue 3 的错误处理
onErrorCaptured((error: Error) => {
  hasError.value = true
  errorDetails.value = error.message
  console.error('<PERSON>rro<PERSON><PERSON>ou<PERSON><PERSON> caught an error:', error)
  return false // 阻止错误继续传播
})

// 重置错误状态
const resetError = () => {
  hasError.value = false
  errorDetails.value = ''
}

// 暴露重置方法
defineExpose({
  resetError
})
</script>

<style scoped>
.render-error {
  background-color: #fee;
  color: #c33;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid #fcc;
}

.render-error h3 {
  margin: 0 0 0.5rem 0;
  font-weight: bold;
}

.render-error details {
  margin-top: 0.5rem;
}

.render-error summary {
  cursor: pointer;
  font-weight: bold;
}

.render-error pre {
  background-color: #f5f5f5;
  padding: 0.5rem;
  border-radius: 0.25rem;
  margin-top: 0.5rem;
  overflow-x: auto;
  font-size: 0.875rem;
}
</style>
