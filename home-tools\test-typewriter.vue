<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">打字机效果测试</h1>
    
    <div class="mb-4">
      <button @click="startTyping" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
        开始实时Markdown渲染
      </button>
      <button @click="stopTyping" class="bg-red-500 text-white px-4 py-2 rounded mr-2">
        停止打字机效果
      </button>
      <button @click="resetContent" class="bg-gray-500 text-white px-4 py-2 rounded mr-2">
        重置内容
      </button>
      <button @click="startFastTyping" class="bg-green-500 text-white px-4 py-2 rounded">
        快速演示
      </button>
    </div>

    <div class="mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded">
      <h3 class="font-semibold text-yellow-800 mb-2">测试说明：</h3>
      <p class="text-yellow-700 text-sm">
        点击"开始实时Markdown渲染"按钮，观察markdown语法在输入过程中如何实时转换为格式化内容。
        注意粗体、斜体、列表、代码块等都会在输入时立即显示正确的格式。
      </p>
    </div>

    <div class="border p-4 rounded">
      <h2 class="text-lg font-semibold mb-2">打字机模式</h2>
      <AiResponseRenderer
        :content="displayContent"
        :is-typing="isTyping"
        font-size="base"
      />
    </div>

    <div class="border p-4 rounded mt-4">
      <h2 class="text-lg font-semibold mb-2">最终模式</h2>
      <AiResponseRenderer
        :content="fullContent"
        :is-typing="false"
        font-size="base"
      />
    </div>
  </div>
</template>

<script setup>
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const fullContent = `# 实时Markdown渲染测试

这是一个**粗体文本**和*斜体文本*的测试。

## 列表测试
- 第一项：这应该在输入时立即显示为列表
- 第二项：**粗体**应该实时生效
- 第三项：\`内联代码\`也应该实时渲染

## 代码块测试
\`\`\`javascript
function hello() {
  console.log("Hello World!");
  // 这个代码块应该在输入时就有语法高亮
}
\`\`\`

## 表格测试
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

## 引用测试
> 这是一个引用块
> 应该在输入时立即显示引用样式

## 链接测试
这是一个[链接](https://example.com)，应该实时渲染。

这是一个包含各种markdown语法的测试内容，所有格式都应该在输入过程中实时显示。`

const displayContent = ref('')
const isTyping = ref(false)
let typingInterval = null

const startTyping = () => {
  isTyping.value = true
  displayContent.value = ''
  let index = 0

  typingInterval = setInterval(() => {
    if (index < fullContent.length) {
      // 不添加光标，让组件自己处理markdown渲染
      displayContent.value = fullContent.slice(0, index + 1)
      index++
    } else {
      displayContent.value = fullContent
      isTyping.value = false
      clearInterval(typingInterval)
    }
  }, 30) // 稍微快一点以便更好地看到实时效果
}

const startFastTyping = () => {
  isTyping.value = true
  displayContent.value = ''
  let index = 0

  typingInterval = setInterval(() => {
    if (index < fullContent.length) {
      displayContent.value = fullContent.slice(0, index + 1)
      index += 3 // 每次跳3个字符，更快演示
    } else {
      displayContent.value = fullContent
      isTyping.value = false
      clearInterval(typingInterval)
    }
  }, 20) // 更快的间隔
}

const stopTyping = () => {
  isTyping.value = false
  if (typingInterval) {
    clearInterval(typingInterval)
    typingInterval = null
  }
  displayContent.value = fullContent
}

const resetContent = () => {
  stopTyping()
  displayContent.value = ''
}

onUnmounted(() => {
  if (typingInterval) {
    clearInterval(typingInterval)
  }
})
</script>
