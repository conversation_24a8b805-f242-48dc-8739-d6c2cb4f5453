<template>
  <div class="p-8">
    <h1 class="text-2xl font-bold mb-4">打字机效果测试</h1>
    
    <div class="mb-4">
      <button @click="startTyping" class="bg-blue-500 text-white px-4 py-2 rounded mr-2">
        开始打字机效果
      </button>
      <button @click="stopTyping" class="bg-red-500 text-white px-4 py-2 rounded mr-2">
        停止打字机效果
      </button>
      <button @click="resetContent" class="bg-gray-500 text-white px-4 py-2 rounded">
        重置内容
      </button>
    </div>

    <div class="border p-4 rounded">
      <h2 class="text-lg font-semibold mb-2">打字机模式</h2>
      <AiResponseRenderer
        :content="displayContent"
        :is-typing="isTyping"
        font-size="base"
      />
    </div>

    <div class="border p-4 rounded mt-4">
      <h2 class="text-lg font-semibold mb-2">最终模式</h2>
      <AiResponseRenderer
        :content="fullContent"
        :is-typing="false"
        font-size="base"
      />
    </div>
  </div>
</template>

<script setup>
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const fullContent = `# 测试标题

这是一个**粗体文本**和*斜体文本*的测试。

## 列表测试
- 第一项
- 第二项
- 第三项

## 代码测试
\`\`\`javascript
function hello() {
  console.log("Hello World!");
}
\`\`\`

## 表格测试
| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

这是一个包含各种markdown语法的测试内容。`

const displayContent = ref('')
const isTyping = ref(false)
let typingInterval = null

const startTyping = () => {
  isTyping.value = true
  displayContent.value = ''
  let index = 0
  
  typingInterval = setInterval(() => {
    if (index < fullContent.length) {
      displayContent.value = fullContent.slice(0, index + 1) + '▊'
      index++
    } else {
      displayContent.value = fullContent
      isTyping.value = false
      clearInterval(typingInterval)
    }
  }, 50)
}

const stopTyping = () => {
  isTyping.value = false
  if (typingInterval) {
    clearInterval(typingInterval)
    typingInterval = null
  }
  displayContent.value = fullContent
}

const resetContent = () => {
  stopTyping()
  displayContent.value = ''
}

onUnmounted(() => {
  if (typingInterval) {
    clearInterval(typingInterval)
  }
})
</script>
