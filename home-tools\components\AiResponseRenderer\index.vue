<template>
  <div :class="`ai-response-container max-w-4xl ${fontSizeClass}`">
    <div v-if="hasError" class="render-error">
      渲染内容时发生错误，请检查内容格式。
    </div>
    <div v-else v-html="renderedContent" />
  </div>
</template>

<script setup lang="ts">
// 动态导入插件，避免 SSR 问题
let marked: any = null
let markedHighlight: any = null
let hljs: any = null

interface Props {
  content: string
  fontSize?: 'sm' | 'base' | 'lg'
}

const props = withDefaults(defineProps<Props>(), {
  fontSize: 'base'
})

// 响应式数据
const renderedContent = ref('')
const hasError = ref(false)

// 性能优化：缓存和防抖
const contentCache = new Map()
const renderingPromise = ref(null)
let renderTimeout = null

// 优化的Emoji处理工具函数 - 预编译正则表达式
const emojiMap: { [key: string]: string } = {
  // 基础表情符号
  '☺': '☺️',
  ':)': '😊',
  ':-)': '😊',
  ':(': '😢',
  ':-(': '😢',
  ':D': '😃',
  ':-D': '😃',
  ';)': '😉',
  ';-)': '😉',
  ':P': '😛',
  ':-P': '😛',
  ':p': '😛',
  ':-p': '😛',
  ':o': '😮',
  ':-o': '😮',
  ':O': '😱',
  ':-O': '😱',
  ':|': '😐',
  ':-|': '😐',
  ':*': '😘',
  ':-*': '😘',
  '<3': '❤️',
  '</3': '💔',
  '~': '～',
  '。。。': '…',
  '...': '…'
}

// 预编译正则表达式
const emojiRegexes = new Map()
Object.entries(emojiMap).forEach(([textEmoji, emoji]) => {
  if (textEmoji === '~') {
    emojiRegexes.set(textEmoji, { regex: /~(?=\s|$)/g, emoji })
  } else if (textEmoji === '。。。' || textEmoji === '...') {
    const escapedText = textEmoji.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    emojiRegexes.set(textEmoji, { regex: new RegExp(escapedText, 'g'), emoji })
  } else if (textEmoji.match(/^[:\-\(\)\[\]<>3pPdDoO\|*]+$/)) {
    emojiRegexes.set(textEmoji, { split: true, emoji })
  }
})

const processEmojis = (text: string): string => {
  let processedText = text

  emojiRegexes.forEach(({ regex, split, emoji }, textEmoji) => {
    if (split) {
      const parts = processedText.split(textEmoji)
      if (parts.length > 1) {
        processedText = parts.join(emoji)
      }
    } else if (regex) {
      processedText = processedText.replace(regex, emoji)
    }
  })

  return processedText
}

// 优化的清理内容函数
const cleanContent = (text: string): string => {
  let cleaned = text.trim()

  // 优化：使用单个正则表达式处理多种情况
  cleaned = cleaned.replace(/(\n\s*){3,}(\|)/g, '\n\n$2')

  // 优化：合并空白字符处理
  cleaned = cleaned.replace(/\s{10,}/g, ' ')

  // 优化：链式处理常规清理
  cleaned = cleaned
    .replace(/\n{3,}/g, '\n\n')
    .replace(/^\s*\n/gm, '\n')
    .replace(/\n\s*$/gm, '\n')
    .replace(/\n\s+\n/g, '\n\n')
    .replace(/(\|.*\|)\n+/g, '$1\n')
    .replace(/\n{2,}(\|)/g, '\n\n$1')

  return cleaned
}

// 字体大小类名
const fontSizeClass = computed(() => {
  return props.fontSize === 'lg' ? 'text-base' : props.fontSize === 'base' ? 'text-sm' : 'text-xs'
})

// 优化的Mermaid图表渲染 - 使用requestAnimationFrame
const renderMermaidDiagrams = () => {
  if (!process.client || !window.mermaid) return

  requestAnimationFrame(() => {
    const mermaid = window.mermaid

    mermaid.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
      fontFamily: 'inherit'
    })

    const containers = document.querySelectorAll('.mermaid-container[data-mermaid]:not(.rendered)')

    for (const container of containers) {
      const code = decodeURIComponent(container.getAttribute('data-mermaid') || '')
      const id = container.id

      // 标记为已处理，避免重复渲染
      container.classList.add('rendered')

      try {
        mermaid.render(id, code).then(({ svg }) => {
          container.innerHTML = svg
        }).catch(error => {
          console.error('Mermaid render error:', error)
          container.innerHTML = `
            <div class="mermaid-error">
              <p>Failed to render diagram</p>
            </div>
          `
        })
      } catch (error) {
        console.error('Mermaid render error:', error)
        container.innerHTML = `
          <div class="mermaid-error">
            <p>Failed to render diagram</p>
          </div>
        `
      }
    }
  })
}

// 优化的LaTeX块渲染 - 使用requestAnimationFrame
const renderLatexBlocks = () => {
  if (!process.client || !window.katex) return

  requestAnimationFrame(() => {
    const katex = window.katex
    const containers = document.querySelectorAll('.latex-block[data-latex]:not(.rendered)')

    for (const container of containers) {
      const code = decodeURIComponent(container.getAttribute('data-latex') || '')

      // 标记为已处理，避免重复渲染
      container.classList.add('rendered')

      try {
        const html = katex.renderToString(code.trim(), { displayMode: true })
        container.innerHTML = html
      } catch (error) {
        console.error('LaTeX render error:', error)
        container.innerHTML = `
          <div class="latex-error">
            <p>LaTeX syntax error</p>
          </div>
        `
      }
    }
  })
}

// 动态加载插件
const loadPlugins = async () => {
  if (!process.client) return false

  try {
    // 动态导入插件
    const markedModule = await import('marked')
    const markedHighlightModule = await import('marked-highlight')
    const hljsModule = await import('highlight.js')

    marked = markedModule.marked
    markedHighlight = markedHighlightModule.markedHighlight
    hljs = hljsModule.default

    // 动态加载 CSS
    if (!document.querySelector('link[href*="highlight.js"]')) {
      await import('highlight.js/styles/vs2015.css')
    }

    return true
  } catch (error) {
    console.error('Failed to load plugins:', error)
    return false
  }
}

// 优化的marked插件配置 - 只配置一次
let markedConfigured = false

const setupMarkedWithPlugins = async () => {
  try {
    // 确保插件已加载
    if (!marked && !(await loadPlugins())) {
      return false
    }

    // 只配置一次
    if (markedConfigured) {
      return true
    }

    // 配置代码高亮插件
    marked.use(markedHighlight({
      langPrefix: 'hljs language-',
      highlight(code: string, lang: string) {
        const language = hljs.getLanguage(lang) ? lang : 'plaintext'
        return hljs.highlight(code, { language }).value
      }
    }))

    // 配置 marked 选项
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: false,
      mangle: false
    })

    // 自定义渲染器
    const renderer = new marked.Renderer()

    // 自定义代码块渲染
    renderer.code = (code: string, language: string | undefined) => {
      if (language === 'mermaid') {
        const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`
        return `<div class="mermaid-container" data-mermaid="${encodeURIComponent(code.trim())}" id="${id}">
          <div class="mermaid-loading">Rendering diagram...</div>
        </div>`
      }

      if (language === 'latex' || language === 'tex') {
        return `<div class="latex-block" data-latex="${encodeURIComponent(code.trim())}">
          <div class="latex-loading">Rendering LaTeX...</div>
        </div>`
      }

      const lang = language || 'plaintext'
      const highlighted = hljs.getLanguage(lang)
        ? hljs.highlight(code, { language: lang }).value
        : hljs.highlightAuto(code).value

      return `<pre class="hljs"><code class="hljs language-${lang}">${highlighted}</code></pre>`
    }

    marked.setOptions({ renderer })
    markedConfigured = true
    return true
  } catch (error) {
    console.error('Failed to setup marked plugins:', error)
    return false
  }
}

// 优化的渲染内容函数 - 添加缓存和防抖
const renderContent = async () => {
  if (!process.client) {
    renderedContent.value = props.content
    return
  }

  // 防抖处理
  if (renderTimeout) {
    clearTimeout(renderTimeout)
  }

  renderTimeout = setTimeout(async () => {
    // 如果已经有渲染任务在进行，等待完成
    if (renderingPromise.value) {
      await renderingPromise.value
    }

    const contentHash = btoa(encodeURIComponent(props.content)).slice(0, 32)

    // 检查缓存
    if (contentCache.has(contentHash)) {
      renderedContent.value = contentCache.get(contentHash)
      nextTick(() => {
        renderMermaidDiagrams()
        renderLatexBlocks()
      })
      return
    }

    // 创建渲染Promise
    renderingPromise.value = performRender(contentHash)
    await renderingPromise.value
    renderingPromise.value = null
  }, 50) // 50ms防抖
}

// 实际的渲染逻辑
const performRender = async (contentHash: string) => {
  try {
    hasError.value = false

    // 清理和预处理内容
    let content = cleanContent(props.content)
    content = processEmojis(content)

    let result = ''
    // 使用插件化的 marked 库
    if (await setupMarkedWithPlugins()) {
      result = marked.parse(content)
    } else {
      // 降级到基础渲染
      result = convertBasicMarkdown(content)
    }

    // 缓存结果
    contentCache.set(contentHash, result)

    // 限制缓存大小
    if (contentCache.size > 50) {
      const firstKey = contentCache.keys().next().value
      contentCache.delete(firstKey)
    }

    renderedContent.value = result

    // 在下一个tick中渲染特殊内容
    nextTick(() => {
      renderMermaidDiagrams()
      renderLatexBlocks()
    })

  } catch (error) {
    console.error('Failed to render content:', error)
    hasError.value = true
    renderedContent.value = props.content
  }
}

// 改进的表格转换函数
const convertTables = (text: string): string => {
  // 匹配markdown表格 - 更精确的正则表达式
  const tableRegex = /^\|(.+)\|\s*\n\|(\s*:?-+:?\s*\|)+\s*\n((\|.+\|\s*\n?)+)/gm

  return text.replace(tableRegex, (match) => {
    const lines = match.trim().split('\n').map(line => line.trim())

    if (lines.length < 3) return match

    const headerLine = lines[0]
    const separatorLine = lines[1]
    const dataLines = lines.slice(2).filter(line => line.trim())

    // 解析表头
    const headers = headerLine.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell)

    // 解析对齐方式
    const alignments = separatorLine.split('|')
      .map(cell => cell.trim())
      .filter(cell => cell)
      .map(cell => {
        if (cell.startsWith(':') && cell.endsWith(':')) return 'center'
        if (cell.endsWith(':')) return 'right'
        return 'left'
      })

    // 解析数据行
    const rows = dataLines.map(line => {
      return line.split('|')
        .map(cell => cell.trim())
        .filter(cell => cell)
    }).filter(row => row.length > 0)

    // 生成HTML表格
    let tableHtml = '<div class="table-container overflow-x-auto my-6 rounded-lg border border-gray-200 shadow-sm">'
    tableHtml += '<table class="min-w-full divide-y divide-gray-200">'

    // 表头
    if (headers.length > 0) {
      tableHtml += '<thead class="bg-gray-50">'
      tableHtml += '<tr>'
      headers.forEach((header, index) => {
        const alignment = alignments[index] || 'left'
        const alignClass = alignment === 'center' ? 'text-center' : alignment === 'right' ? 'text-right' : 'text-left'
        tableHtml += `<th class="px-6 py-3 ${alignClass} text-xs font-medium text-gray-500 uppercase tracking-wider">${header}</th>`
      })
      tableHtml += '</tr>'
      tableHtml += '</thead>'
    }

    // 表体
    if (rows.length > 0) {
      tableHtml += '<tbody class="bg-white divide-y divide-gray-200">'
      rows.forEach((row, rowIndex) => {
        const rowClass = rowIndex % 2 === 0 ? 'bg-white' : 'bg-gray-50'
        tableHtml += `<tr class="${rowClass}">`
        row.forEach((cell, cellIndex) => {
          const alignment = alignments[cellIndex] || 'left'
          const alignClass = alignment === 'center' ? 'text-center' : alignment === 'right' ? 'text-right' : 'text-left'
          tableHtml += `<td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 ${alignClass}">${cell}</td>`
        })
        tableHtml += '</tr>'
      })
      tableHtml += '</tbody>'
    }

    tableHtml += '</table></div>'

    return tableHtml
  })
}

// 检测内容类型
const detectContentType = (text: string): 'html' | 'markdown' | 'mixed' => {
  const htmlTagRegex = /<\/?[a-z][\s\S]*>/i
  const markdownRegex = /^(#{1,6}\s|>\s|\*\s|\d+\.\s|```|\|.*\|)/m

  const hasHtmlTags = htmlTagRegex.test(text)
  const hasMarkdownSyntax = markdownRegex.test(text)

  if (hasHtmlTags && hasMarkdownSyntax) {
    return 'mixed'
  } else if (hasHtmlTags) {
    return 'html'
  } else if (hasMarkdownSyntax) {
    return 'markdown'
  } else {
    return 'markdown' // 默认按 markdown 处理
  }
}

// 预处理混合内容
const preprocessMixedContent = (text: string): { processedText: string, htmlBlocks: string[] } => {
  // 保护已有的HTML标签，避免被markdown处理器影响
  const htmlBlocks: string[] = []
  let processedText = text

  // 保护完整的HTML块（如div、section等）
  processedText = processedText.replace(/<(div|section|article|aside|header|footer|nav|main)[^>]*>[\s\S]*?<\/\1>/gi, (match) => {
    const placeholder = `__HTML_BLOCK_${htmlBlocks.length}__`
    htmlBlocks.push(match)
    return placeholder
  })

  // 保护单个HTML标签
  processedText = processedText.replace(/<[^>]+>/g, (match) => {
    const placeholder = `__HTML_TAG_${htmlBlocks.length}__`
    htmlBlocks.push(match)
    return placeholder
  })

  return { processedText, htmlBlocks }
}

// 恢复HTML内容
const restoreHtmlContent = (text: string, htmlBlocks: string[]): string => {
  let restoredText = text

  htmlBlocks.forEach((block, index) => {
    const placeholder = `__HTML_BLOCK_${index}__`
    const tagPlaceholder = `__HTML_TAG_${index}__`
    restoredText = restoredText.replace(placeholder, block)
    restoredText = restoredText.replace(tagPlaceholder, block)
  })

  return restoredText
}

// 增强的markdown转换函数
const convertEnhancedMarkdown = (text: string): string => {
  const contentType = detectContentType(text)

  if (contentType === 'html') {
    // 纯HTML内容，直接返回
    return text
  }

  if (contentType === 'mixed') {
    // 混合内容，需要特殊处理
    const { processedText, htmlBlocks } = preprocessMixedContent(text)
    const markdownHtml = convertBasicMarkdown(processedText)
    return restoreHtmlContent(markdownHtml, htmlBlocks)
  }

  // 纯markdown内容
  return convertBasicMarkdown(text)
}

// 改进的基础markdown转换函数
const convertBasicMarkdown = (text: string): string => {
  let html = text

  // 先处理代码块，避免其内容被其他规则影响
  html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, lang, code) => {
    if (lang === 'mermaid') {
      const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`
      return `<div class="mermaid-container" data-mermaid="${encodeURIComponent(code.trim())}" id="${id}">
        <div class="mermaid-loading">Rendering diagram...</div>
      </div>`
    }

    if (lang === 'latex' || lang === 'tex') {
      return `<div class="latex-block" data-latex="${encodeURIComponent(code.trim())}">
        <div class="latex-loading">Rendering LaTeX...</div>
      </div>`
    }

    return `<div class="code-block-wrapper my-4">
      <pre class="hljs bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
        <code class="hljs language-${lang || 'plaintext'}">${escapeHtml(code.trim())}</code>
      </pre>
    </div>`
  })

  // 处理内联代码（在其他处理之前）
  html = html.replace(/`([^`\n]+)`/g, '<code class="inline-code bg-gray-100 text-gray-800 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>')

  // 转换水平分割线
  html = html.replace(/^---+\s*$/gm, '<hr class="my-6 border-gray-300">')
  html = html.replace(/^\*\*\*+\s*$/gm, '<hr class="my-6 border-gray-300">')

  // 转换标题
  html = html.replace(/^#{6}\s+(.+)$/gm, '<h6 class="text-sm font-medium mt-4 mb-2 text-gray-700">$1</h6>')
  html = html.replace(/^#{5}\s+(.+)$/gm, '<h5 class="text-base font-medium mt-4 mb-2 text-gray-700">$1</h5>')
  html = html.replace(/^#{4}\s+(.+)$/gm, '<h4 class="text-lg font-medium mt-4 mb-2 text-gray-800">$1</h4>')
  html = html.replace(/^#{3}\s+(.+)$/gm, '<h3 class="text-xl font-semibold mt-5 mb-3 text-gray-800">$1</h3>')
  html = html.replace(/^#{2}\s+(.+)$/gm, '<h2 class="text-2xl font-semibold mt-6 mb-4 text-gray-900">$1</h2>')
  html = html.replace(/^#{1}\s+(.+)$/gm, '<h1 class="text-3xl font-bold mt-8 mb-6 text-gray-900">$1</h1>')

  // 转换引用块（支持多行引用和嵌套）
  html = html.replace(/^>\s*(.+)$/gm, (match, content) => {
    return `<blockquote class="border-l-4 border-blue-400 pl-4 py-2 my-4 bg-blue-50 text-gray-700 italic">${content}</blockquote>`
  })

  // 转换粗体和斜体（在列表之前处理，避免冲突）
  html = html.replace(/\*\*\*(.+?)\*\*\*/g, '<strong class="font-bold"><em class="italic">$1</em></strong>')
  html = html.replace(/___(.+?)___/g, '<strong class="font-bold"><em class="italic">$1</em></strong>')
  html = html.replace(/\*\*(.+?)\*\*/g, '<strong class="font-semibold text-gray-900">$1</strong>')
  html = html.replace(/__(.+?)__/g, '<strong class="font-semibold text-gray-900">$1</strong>')
  html = html.replace(/\*(.+?)\*/g, '<em class="italic text-gray-700">$1</em>')
  html = html.replace(/_(.+?)_/g, '<em class="italic text-gray-700">$1</em>')

  // 转换删除线
  html = html.replace(/~~(.+?)~~/g, '<del class="line-through text-gray-500 opacity-75">$1</del>')

  // 转换链接（在列表之前处理）
  html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-600 hover:text-blue-800 underline transition-colors" target="_blank" rel="noopener noreferrer">$1</a>')

  // 转换图片
  html = html.replace(/!\[([^\]]*)\]\(([^)]+)\)/g, '<div class="image-container my-6 text-center"><img src="$2" alt="$1" class="max-w-full h-auto rounded-lg shadow-lg mx-auto"><div class="image-caption mt-2 text-sm text-gray-600 italic">$1</div></div>')

  // 转换表格
  html = convertTables(html)

  // 转换有序列表
  html = html.replace(/^\d+\.\s+(.+)$/gm, '<li class="mb-1">$1</li>')

  // 转换无序列表
  html = html.replace(/^[\*\-\+]\s+(.+)$/gm, '<li class="mb-1">$1</li>')

  // 将连续的li包装在ul或ol中
  html = html.replace(/(<li[^>]*>.*?<\/li>\s*)+/gs, (match) => {
    return `<ul class="list-disc list-inside my-4 ml-6 space-y-1 text-gray-700">${match}</ul>`
  })

  // 转换自动链接
  html = html.replace(/(^|[^"])(https?:\/\/[^\s<>"]+)/g, '$1<a href="$2" class="text-blue-600 hover:text-blue-800 underline break-all" target="_blank" rel="noopener noreferrer">$2</a>')

  // 处理段落 - 改进的段落处理
  const lines = html.split('\n')
  const processedLines: string[] = []
  let inParagraph = false
  let paragraphContent = ''

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim()

    // 空行
    if (!line) {
      if (inParagraph) {
        processedLines.push(`<p class="text-base leading-7 my-4 text-gray-700">${paragraphContent.trim()}</p>`)
        paragraphContent = ''
        inParagraph = false
      }
      continue
    }

    // HTML标签行
    if (line.match(/^<(h[1-6]|div|blockquote|ul|ol|li|hr|pre|table|img)/)) {
      if (inParagraph) {
        processedLines.push(`<p class="text-base leading-7 my-4 text-gray-700">${paragraphContent.trim()}</p>`)
        paragraphContent = ''
        inParagraph = false
      }
      processedLines.push(line)
      continue
    }

    // 普通文本行
    if (inParagraph) {
      paragraphContent += ' ' + line
    } else {
      paragraphContent = line
      inParagraph = true
    }
  }

  // 处理最后的段落
  if (inParagraph && paragraphContent.trim()) {
    processedLines.push(`<p class="text-base leading-7 my-4 text-gray-700">${paragraphContent.trim()}</p>`)
  }

  return processedLines.join('\n')
}

// HTML转义函数
const escapeHtml = (text: string): string => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}

// 使用CDN版本的库进行插件化渲染
const renderWithCDNLibs = async () => {
  if (!process.client) return

  try {
    // 等待CDN库加载完成
    await waitForCDNLibs()

    // 使用插件化的marked库
    if (await setupMarkedWithPlugins()) {
      let content = cleanContent(props.content)
      content = processEmojis(content)

      renderedContent.value = marked.parse(content)

      // 渲染特殊内容
      nextTick(() => {
        renderMermaidDiagrams()
        renderLatexBlocks()
      })
    }
  } catch (error) {
    console.error('Failed to render with CDN libs:', error)
    // 降级到基础渲染
    await renderContent()
  }
}

// 等待CDN库加载
const waitForCDNLibs = (): Promise<void> => {
  return new Promise((resolve) => {
    const checkLibs = () => {
      if (window.marked && window.mermaid && window.katex) {
        resolve()
      } else {
        setTimeout(checkLibs, 0)
      }
    }
    checkLibs()
  })
}





// 初始化渲染
onMounted(async () => {
  // 使用插件化的 marked
  await renderContent()
})

// 优化的内容变化监听 - 添加深度比较
watch(() => props.content, async (newContent, oldContent) => {
  // 避免不必要的重新渲染
  if (newContent === oldContent) return
  await renderContent()
}, { flush: 'post' }) // 在DOM更新后执行

// 清理函数
onUnmounted(() => {
  if (renderTimeout) {
    clearTimeout(renderTimeout)
  }
  contentCache.clear()
})
</script>

<style scoped>
/* AI响应容器基础样式 */
.ai-response-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.6;
  color: #374151;
}

/* 代码高亮样式 - 使用 highlight.js 的 vs2015 主题 */
:deep(.hljs) {
  display: block;
  overflow-x: auto;
  padding: 1rem;
  margin: 1rem 0;
  border-radius: 0.375rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Consolas', 'Courier New', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 代码块容器样式 */
:deep(pre) {
  margin: 1rem 0;
  border-radius: 0.375rem;
  overflow-x: auto;
}

:deep(pre code) {
  background: transparent;
  border: none;
  padding: 0;
  margin: 0;
  display: block;
}

/* 内联代码样式 - 标准样式 */
:deep(code) {
  background-color: rgba(175, 184, 193, 0.2);
  border-radius: 0.1875rem;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 85%;
  margin: 0;
  padding: 0.2em 0.4em;
}

/* 段落样式 */
:deep(p) {
  margin: 0 0 1rem 0;
  line-height: 1.6;
  color: #24292e;
}

/* 标题样式 - 标准 GitHub 样式 */
:deep(h1) {
  font-size: 2em;
  font-weight: 600;
  margin: 0 0 1rem 0;
  color: #24292e;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

:deep(h2) {
  font-size: 1.5em;
  font-weight: 600;
  margin: 1.5em 0 1rem 0;
  color: #24292e;
  line-height: 1.25;
  border-bottom: 1px solid #eaecef;
  padding-bottom: 0.3em;
}

:deep(h3) {
  font-size: 1.25em;
  font-weight: 600;
  margin: 1.5em 0 1rem 0;
  color: #24292e;
  line-height: 1.25;
}

:deep(h4) {
  font-size: 1em;
  font-weight: 600;
  margin: 1.5em 0 1rem 0;
  color: #24292e;
  line-height: 1.25;
}

:deep(h5) {
  font-size: 0.875em;
  font-weight: 600;
  margin: 1.5em 0 1rem 0;
  color: #24292e;
  line-height: 1.25;
}

:deep(h6) {
  font-size: 0.85em;
  font-weight: 600;
  margin: 1.5em 0 1rem 0;
  color: #6a737d;
  line-height: 1.25;
}

/* 列表样式 - 标准样式 */
:deep(ul), :deep(ol) {
  margin: 0 0 1rem 0;
  padding-left: 2em;
}

:deep(li) {
  margin: 0.25em 0;
  line-height: 1.6;
  color: #24292e;
}

/* 引用块样式 - 标准样式 */
:deep(blockquote) {
  border-left: 0.25em solid #dfe2e5;
  color: #6a737d;
  padding: 0 1em;
  margin: 0 0 1rem 0;
}

:deep(blockquote p) {
  margin: 0 0 1rem 0;
}

:deep(blockquote p:last-child) {
  margin-bottom: 0;
}

/* 链接样式 */
:deep(a) {
  color: #2563eb;
  text-decoration: underline;
  transition: color 0.2s ease;
}

:deep(a:hover) {
  color: #1d4ed8;
}

/* 强调文本样式 */
:deep(strong) {
  font-weight: 600;
  color: #111827;
}

:deep(em) {
  font-style: italic;
  color: #4b5563;
}

/* 表格样式 - 标准 GitHub 样式 */
:deep(table) {
  border-collapse: collapse;
  border-spacing: 0;
  width: 100%;
  margin: 0 0 1rem 0;
  overflow: auto;
}

:deep(table th) {
  font-weight: 600;
  background-color: #f6f8fa;
  border: 1px solid #d0d7de;
  padding: 6px 13px;
}

:deep(table td) {
  border: 1px solid #d0d7de;
  padding: 6px 13px;
}

:deep(table tr) {
  background-color: #ffffff;
  border-top: 1px solid #d0d7de;
}

:deep(table tr:nth-child(2n)) {
  background-color: #f6f8fa;
}

/* 图片样式 */
:deep(.image-container) {
  margin: 1rem 0;
  text-align: center;
}

:deep(.image-container img) {
  max-width: 100%;
  height: auto;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

:deep(.image-caption) {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  color: #6b7280;
  font-style: italic;
}

/* Mermaid样式 */
:deep(.mermaid-container) {
  margin: 1.5rem 0;
  text-align: center;
  background-color: #fafafa;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

:deep(.mermaid-loading) {
  padding: 2rem;
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
}

:deep(.mermaid-error) {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  text-align: left;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* LaTeX样式 */
:deep(.latex-block) {
  text-align: center;
  margin: 1.5rem 0;
  background-color: #fafafa;
  border-radius: 0.5rem;
  padding: 1rem;
  border: 1px solid #e5e7eb;
}

:deep(.latex-loading) {
  padding: 1rem;
  color: #6b7280;
  font-style: italic;
  font-size: 0.875rem;
}

:deep(.latex-error) {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* KaTeX渲染后的样式 */
:deep(.katex-display) {
  margin: 1rem 0;
  text-align: center;
}

:deep(.katex) {
  font-size: 1.1em;
}

/* 分割线样式 */
:deep(hr) {
  border: none;
  border-top: 1px solid #e5e7eb;
  margin: 1.5rem 0;
}

/* 删除线样式 */
:deep(del) {
  text-decoration: line-through;
  color: #6b7280;
  opacity: 0.7;
}

/* 混合内容样式 */
:deep(.mixed-content) {
  display: block;
}

/* HTML块样式 */
:deep(.html-block) {
  margin: 1rem 0;
}

/* 自动链接样式 */
:deep(a[href^="http"]) {
  word-break: break-all;
}

/* 无高度的br标签 */
:deep(br.no-height) {
  line-height: 0;
  height: 0;
  margin: 0;
  padding: 0;
  display: inline;
}

/* 错误样式 */
.render-error {
  background-color: #fef2f2;
  color: #dc2626;
  padding: 1rem;
  border-radius: 0.5rem;
  margin: 0.5rem 0;
  border: 1px solid #fecaca;
  font-size: 0.875rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .ai-response-container {
    font-size: 0.875rem;
  }

  :deep(.hljs) {
    padding: 0.75rem;
    font-size: 0.8rem;
  }

  :deep(.table-container) {
    margin-left: -1rem;
    margin-right: -1rem;
    font-size: 0.8rem;
  }

  :deep(h1) {
    font-size: 1.25rem;
  }

  :deep(h2) {
    font-size: 1.125rem;
  }

  :deep(h3) {
    font-size: 1rem;
  }

  :deep(p) {
    font-size: 0.875rem;
    line-height: 1.6;
  }

  :deep(.mermaid-container),
  :deep(.latex-block) {
    margin-left: -0.5rem;
    margin-right: -0.5rem;
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 打印样式 */
@media print {
  .ai-response-container {
    color: #000;
  }

  :deep(.hljs) {
    background: #f8f9fa !important;
    color: #000 !important;
    border: 1px solid #dee2e6;
  }

  :deep(.mermaid-container),
  :deep(.latex-block) {
    break-inside: avoid;
  }
}
</style>
