<template>
  <!-- SEO专用的qa-list内容容器 - 对搜索引擎可见，对用户隐藏 -->
  <div
    v-if="qaData && qaData.length > 0"
    class="seo-qa-content"
    aria-hidden="true"
    style="position: absolute; left: -9999px; width: 1px; height: 1px; overflow: hidden; clip: rect(0, 0, 0, 0); white-space: nowrap;"
  >

    <!-- 完整的qa-list对话内容 -->
    <div class="qa-conversation">
      <h1>{{ caseTitle || '案例对话' }}</h1>
      <div class="qa-list">
        <div 
          v-for="(item, index) in qaData" 
          :key="`qa-${index}`"
          class="qa-item"
        >
          <!-- 用户问题 -->
          <div v-if="item.query" class="question">
            <h2>{{ `问题 ${index + 1}` }}</h2>
            <p>{{ item.query }}</p>
          </div>
          
          <!-- AI回答 -->
          <div v-if="item.answer" class="answer">
            <h3>{{ `回答 ${index + 1}` }}</h3>
            <div v-html="formatAnswer(item.answer)"></div>
          </div>
        </div>
      </div>
      
      <!-- 额外的SEO关键词和描述 -->
      <div class="seo-metadata">
        <p>案例ID: {{ caseId }}</p>
        <p>医学AI智能对话，专业医疗问答，梅斯小智</p>
        <p>关键词: 医学AI, 智能问答, 医疗咨询, 案例分析, 梅斯医学</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface QAItem {
  query: string
  answer: string
}

interface Props {
  caseId: string
  caseTitle?: string
  qaData: QAItem[]
}

const props = withDefaults(defineProps<Props>(), {
  caseTitle: '案例对话',
  qaData: () => []
})

// 格式化回答内容，确保HTML标签正确显示
const formatAnswer = (answer: string): string => {
  if (!answer) return ''

  // 如果答案包含HTML标签，直接返回
  if (answer.includes('<') && answer.includes('>')) {
    return answer
  }

  // 否则将换行符转换为<br>标签
  return answer.replace(/\n/g, '<br>')
}

// 生成结构化数据
const structuredData = computed(() => {
  if (!props.qaData || props.qaData.length === 0) return null

  return {
    "@context": "https://schema.org",
    "@type": "QAPage",
    "mainEntity": props.qaData.map((item, index) => ({
      "@type": "Question",
      "name": item.query || `问题 ${index + 1}`,
      "text": item.query || `问题 ${index + 1}`,
      "answerCount": 1,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": item.answer || "暂无回答",
        "author": {
          "@type": "Organization",
          "name": "梅斯小智"
        }
      }
    }))
  }
})

// 使用useHead添加结构化数据
useHead({
  script: [
    {
      type: 'application/ld+json',
      innerHTML: () => structuredData.value ? JSON.stringify(structuredData.value) : ''
    }
  ]
})
</script>

<style scoped>
/* 确保SEO内容完全隐藏但仍可被搜索引擎访问 */
.seo-qa-content {
  /* 使用多种隐藏方式确保兼容性 */
  position: absolute !important;
  left: -9999px !important;
  top: -9999px !important;
  width: 1px !important;
  height: 1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* 为搜索引擎提供良好的内容结构 */
.qa-conversation h1 {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
}

.qa-conversation h2 {
  font-size: 18px;
  font-weight: bold;
  margin: 15px 0 10px 0;
  color: #333;
}

.qa-conversation h3 {
  font-size: 16px;
  font-weight: bold;
  margin: 10px 0 8px 0;
  color: #666;
}

.qa-item {
  margin-bottom: 30px;
  border-bottom: 1px solid #eee;
  padding-bottom: 20px;
}

.question {
  margin-bottom: 15px;
}

.answer {
  margin-bottom: 15px;
}

.question p,
.answer div {
  line-height: 1.6;
  margin: 8px 0;
}

.seo-metadata {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.seo-metadata p {
  margin: 5px 0;
  color: #888;
  font-size: 14px;
}
</style>
