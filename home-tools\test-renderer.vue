<template>
  <div class="test-container p-4">
    <h1 class="text-2xl font-bold mb-4">AiResponseRenderer 测试页面</h1>
    
    <div class="mb-6">
      <h2 class="text-xl font-semibold mb-2">选择测试内容类型：</h2>
      <div class="flex gap-2 mb-4">
        <button 
          v-for="(test, key) in testCases" 
          :key="key"
          @click="currentTest = key"
          :class="[
            'px-4 py-2 rounded border',
            currentTest === key 
              ? 'bg-blue-500 text-white border-blue-500' 
              : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
          ]"
        >
          {{ test.name }}
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 原始内容 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-medium mb-2">原始内容</h3>
        <pre class="bg-gray-100 p-3 rounded text-sm overflow-auto max-h-96">{{ testCases[currentTest].content }}</pre>
      </div>

      <!-- 渲染结果 -->
      <div class="border rounded-lg p-4">
        <h3 class="text-lg font-medium mb-2">渲染结果</h3>
        <div class="border bg-white p-3 rounded max-h-96 overflow-auto">
          <AiResponseRenderer :content="testCases[currentTest].content" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const currentTest = ref('markdown')

const testCases = {
  markdown: {
    name: 'Markdown',
    content: `# 这是一级标题

## 这是二级标题

### 这是三级标题

这是一个普通段落，包含**粗体文本**和*斜体文本*。

> 这是一个引用块
> 可以包含多行内容

- 无序列表项1
- 无序列表项2
- 无序列表项3

1. 有序列表项1
2. 有序列表项2
3. 有序列表项3

\`这是内联代码\`

\`\`\`javascript
// 这是代码块
function hello() {
  console.log('Hello World!');
}
\`\`\`

[这是链接](https://example.com)

![这是图片](https://via.placeholder.com/300x200)

| 表头1 | 表头2 | 表头3 |
|-------|-------|-------|
| 单元格1 | 单元格2 | 单元格3 |
| 单元格4 | 单元格5 | 单元格6 |

---

~~删除线文本~~`
  },
  
  html: {
    name: 'HTML',
    content: `<div class="custom-container">
  <h1 style="color: blue;">HTML 标题</h1>
  <p>这是一个 <strong>HTML 段落</strong>，包含 <em>强调文本</em>。</p>
  
  <div class="alert alert-info">
    <p>这是一个自定义的提示框</p>
  </div>
  
  <ul>
    <li>HTML 列表项 1</li>
    <li>HTML 列表项 2</li>
  </ul>
  
  <table border="1" style="border-collapse: collapse;">
    <tr>
      <th>列1</th>
      <th>列2</th>
    </tr>
    <tr>
      <td>数据1</td>
      <td>数据2</td>
    </tr>
  </table>
</div>`
  },
  
  mixed: {
    name: '混合内容',
    content: `# Markdown 标题

这是一个 **Markdown 段落**。

<div style="background: #f0f8ff; padding: 10px; border-radius: 5px; margin: 10px 0;">
  <h3>HTML 容器</h3>
  <p>这是嵌入在 Markdown 中的 <strong>HTML 内容</strong>。</p>
</div>

## 继续 Markdown

- Markdown 列表项
- 另一个列表项

<ul style="color: red;">
  <li>HTML 红色列表项</li>
  <li>另一个红色列表项</li>
</ul>

\`\`\`javascript
// Markdown 代码块
const mixed = true;
\`\`\`

<pre style="background: #ffffcc; padding: 10px;">
HTML 预格式化文本
保持原有格式
</pre>

最后是一个 Markdown 链接：[点击这里](https://example.com)`
  },
  
  emoji: {
    name: 'Emoji 测试',
    content: `# Emoji 表情测试 😊

基础表情符号转换：
- :) 转换为笑脸
- :( 转换为哭脸
- :D 转换为大笑
- ;) 转换为眨眼
- <3 转换为爱心
- </3 转换为破碎的心

**原生 Emoji：** 🎉 🚀 💡 ⭐ 🔥

**文本省略号：** 这是一个很长的句子...

**波浪号：** 这样很好~ 不错~

混合使用：
> 引用中的表情 :) 和原生 emoji 🎯

\`代码中的表情不会转换 :)\``
  }
}
</script>

<style scoped>
.test-container {
  max-width: 1200px;
  margin: 0 auto;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
