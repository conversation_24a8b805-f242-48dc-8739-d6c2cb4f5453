<template>
  <div class="example-container p-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-6">混合内容渲染示例</h1>
    
    <div class="mb-6">
      <label class="block text-sm font-medium mb-2">选择示例内容：</label>
      <select 
        v-model="selectedExample" 
        class="border border-gray-300 rounded px-3 py-2 w-full max-w-xs"
      >
        <option value="aiResponse">AI 助手回复</option>
        <option value="documentation">技术文档</option>
        <option value="blogPost">博客文章</option>
        <option value="tutorial">教程内容</option>
      </select>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 输入区域 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">原始内容</h2>
        <textarea 
          v-model="examples[selectedExample].content"
          class="w-full h-96 p-3 border border-gray-300 rounded font-mono text-sm"
          placeholder="在这里输入 Markdown 或 HTML 内容..."
        />
        <div class="text-sm text-gray-600">
          支持 Markdown、HTML 或两者混合的内容
        </div>
      </div>

      <!-- 渲染结果 -->
      <div class="space-y-4">
        <h2 class="text-xl font-semibold">渲染结果</h2>
        <div class="border border-gray-300 rounded p-4 bg-white h-96 overflow-auto">
          <AiResponseRenderer 
            :content="examples[selectedExample].content" 
            :fontSize="fontSize"
          />
        </div>
        <div class="flex items-center gap-4">
          <label class="text-sm font-medium">字体大小：</label>
          <select v-model="fontSize" class="border border-gray-300 rounded px-2 py-1 text-sm">
            <option value="sm">小</option>
            <option value="base">中</option>
            <option value="lg">大</option>
          </select>
        </div>
      </div>
    </div>

    <!-- 功能说明 -->
    <div class="mt-8 p-4 bg-blue-50 rounded-lg">
      <h3 class="text-lg font-semibold mb-2">功能特性</h3>
      <ul class="text-sm space-y-1">
        <li>✅ 自动检测内容类型（Markdown、HTML、混合）</li>
        <li>✅ 支持完整的 Markdown 语法</li>
        <li>✅ 保持 HTML 标签和样式</li>
        <li>✅ Emoji 表情符号转换</li>
        <li>✅ 代码高亮和特殊内容渲染</li>
        <li>✅ 响应式设计，移动端友好</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import AiResponseRenderer from '../components/AiResponseRenderer/index.vue'

const selectedExample = ref('aiResponse')
const fontSize = ref('base')

const examples = reactive({
  aiResponse: {
    content: `# AI 助手回复示例

您好！我是您的 AI 助手。以下是关于 **Vue.js 组件开发** 的一些建议：

## 组件设计原则

1. **单一职责**：每个组件只负责一个功能
2. **可复用性**：设计时考虑复用场景
3. **可维护性**：代码清晰，易于理解

<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin: 20px 0;">
  <h3 style="margin: 0 0 10px 0;">💡 专业提示</h3>
  <p style="margin: 0;">使用 TypeScript 可以提高代码质量和开发效率！</p>
</div>

### 代码示例

\`\`\`vue
<template>
  <div class="my-component">
    <h1>{{ title }}</h1>
    <slot />
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
}

defineProps<Props>()
</script>
\`\`\`

> **注意**：记得在组件中使用 \`defineProps\` 来定义属性类型。

希望这些信息对您有帮助！如果您有任何问题，请随时询问 😊`
  },

  documentation: {
    content: `# API 文档

## 用户管理接口

### 获取用户列表

<div class="api-endpoint">
  <span class="method get">GET</span>
  <code>/api/users</code>
</div>

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| page | number | 否 | 页码，默认为 1 |
| limit | number | 否 | 每页数量，默认为 10 |
| search | string | 否 | 搜索关键词 |

**响应示例：**

\`\`\`json
{
  "code": 200,
  "message": "success",
  "data": {
    "users": [
      {
        "id": 1,
        "name": "张三",
        "email": "<EMAIL>"
      }
    ],
    "total": 100
  }
}
\`\`\`

<div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 15px 0;">
  <strong>⚠️ 注意事项：</strong>
  <ul style="margin: 10px 0 0 20px;">
    <li>需要在请求头中包含有效的 Authorization token</li>
    <li>limit 参数最大值为 100</li>
  </ul>
</div>

### 错误码说明

- \`400\`: 请求参数错误
- \`401\`: 未授权访问
- \`403\`: 权限不足
- \`404\`: 资源不存在
- \`500\`: 服务器内部错误`
  },

  blogPost: {
    content: `# 前端开发最佳实践

*发布时间：2024年1月15日*

在现代前端开发中，选择合适的技术栈和遵循最佳实践至关重要。

## 技术选型

### 框架对比

<table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
  <thead style="background: #f8f9fa;">
    <tr>
      <th style="border: 1px solid #dee2e6; padding: 12px;">框架</th>
      <th style="border: 1px solid #dee2e6; padding: 12px;">优势</th>
      <th style="border: 1px solid #dee2e6; padding: 12px;">适用场景</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td style="border: 1px solid #dee2e6; padding: 12px;"><strong>Vue.js</strong></td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">学习曲线平缓，文档完善</td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">中小型项目，快速原型</td>
    </tr>
    <tr>
      <td style="border: 1px solid #dee2e6; padding: 12px;"><strong>React</strong></td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">生态丰富，社区活跃</td>
      <td style="border: 1px solid #dee2e6; padding: 12px;">大型应用，复杂交互</td>
    </tr>
  </tbody>
</table>

## 性能优化技巧

1. **代码分割**：使用动态导入减少初始包大小
2. **懒加载**：按需加载组件和资源
3. **缓存策略**：合理使用浏览器缓存

<div style="background: #d4edda; border-left: 4px solid #28a745; padding: 15px; margin: 20px 0;">
  <h4 style="color: #155724; margin: 0 0 10px 0;">💡 性能提示</h4>
  <p style="color: #155724; margin: 0;">使用 Lighthouse 定期检测网站性能，保持 90+ 的评分。</p>
</div>

### 工具推荐

- **构建工具**：Vite、Webpack
- **代码质量**：ESLint、Prettier
- **测试框架**：Vitest、Jest

\`\`\`bash
# 安装推荐的开发依赖
npm install -D vite eslint prettier vitest
\`\`\`

---

**总结**：选择适合项目的技术栈，遵循最佳实践，持续学习新技术 🚀`
  },

  tutorial: {
    content: `# Vue 3 组合式 API 教程

欢迎学习 Vue 3 的组合式 API！本教程将带您从基础到进阶。

## 第一步：设置开发环境

<div style="background: #e3f2fd; border-radius: 8px; padding: 20px; margin: 20px 0;">
  <h3 style="color: #1976d2; margin-top: 0;">🛠️ 环境要求</h3>
  <ul style="color: #1565c0;">
    <li>Node.js 16.0 或更高版本</li>
    <li>现代浏览器（支持 ES6+）</li>
    <li>代码编辑器（推荐 VS Code）</li>
  </ul>
</div>

### 创建项目

\`\`\`bash
# 使用 Vite 创建 Vue 项目
npm create vue@latest my-vue-app
cd my-vue-app
npm install
npm run dev
\`\`\`

## 第二步：理解组合式 API

### 基础语法

\`\`\`vue
<template>
  <div>
    <h1>{{ title }}</h1>
    <p>计数器：{{ count }}</p>
    <button @click="increment">增加</button>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

// 响应式数据
const count = ref(0)
const title = ref('我的应用')

// 计算属性
const doubleCount = computed(() => count.value * 2)

// 方法
const increment = () => {
  count.value++
}
</script>
\`\`\`

<div style="background: #fff3e0; border-left: 4px solid #ff9800; padding: 15px; margin: 20px 0;">
  <strong style="color: #e65100;">📝 重要概念</strong>
  <p style="color: #ef6c00; margin: 10px 0 0 0;">
    在组合式 API 中，使用 <code>ref()</code> 创建响应式数据，
    使用 <code>.value</code> 访问和修改值。
  </p>
</div>

## 第三步：生命周期钩子

| 选项式 API | 组合式 API | 说明 |
|------------|------------|------|
| beforeCreate | - | 使用 setup() |
| created | - | 使用 setup() |
| beforeMount | onBeforeMount | 挂载前 |
| mounted | onMounted | 挂载后 |
| beforeUpdate | onBeforeUpdate | 更新前 |
| updated | onUpdated | 更新后 |

### 实际应用

\`\`\`vue
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'

let timer: number

onMounted(() => {
  console.log('组件已挂载')
  timer = setInterval(() => {
    console.log('定时器执行')
  }, 1000)
})

onUnmounted(() => {
  console.log('组件即将卸载')
  clearInterval(timer)
})
</script>
\`\`\`

## 练习任务

<ol style="background: #f3e5f5; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <li style="margin-bottom: 10px;">创建一个待办事项列表组件</li>
  <li style="margin-bottom: 10px;">实现添加、删除、标记完成功能</li>
  <li style="margin-bottom: 10px;">使用 localStorage 持久化数据</li>
  <li>添加过滤功能（全部、已完成、未完成）</li>
</ol>

> **提示**：可以使用 \`watchEffect\` 来监听数据变化并自动保存到 localStorage。

祝您学习愉快！🎉`
  }
})
</script>

<style scoped>
.example-container {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

textarea {
  resize: vertical;
  min-height: 200px;
}

.api-endpoint {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin: 10px 0;
}

.method {
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: bold;
  font-size: 12px;
  text-transform: uppercase;
}

.method.get {
  background: #e8f5e8;
  color: #2e7d32;
}
</style>
