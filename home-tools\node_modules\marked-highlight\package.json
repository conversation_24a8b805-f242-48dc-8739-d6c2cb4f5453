{"name": "marked-highlight", "version": "2.2.2", "description": "marked highlight", "main": "./lib/index.cjs", "module": "./src/index.js", "browser": "./lib/index.umd.js", "types": "./src/index.d.ts", "type": "module", "keywords": ["marked", "extension", "highlight"], "files": ["lib/", "src/"], "exports": {".": {"types": "./src/index.d.ts", "import": "./src/index.js", "require": "./lib/index.cjs"}}, "scripts": {"test": "jest --verbose", "test:cover": "jest --coverage", "test:types": "tsd -t src/index.d.ts -f types_test/index.test-d.ts", "lint": "eslint", "build": "rollup -c rollup.config.js"}, "repository": {"type": "git", "url": "git+https://github.com/markedjs/marked-highlight.git"}, "author": "<PERSON> <<PERSON>@Brix.ninja> (https://Tony.Brix.ninja)", "license": "MIT", "bugs": {"url": "https://github.com/markedjs/marked-highlight/issues"}, "homepage": "https://github.com/markedjs/marked-highlight#readme", "peerDependencies": {"marked": ">=4 <17"}, "devDependencies": {"@babel/core": "^7.27.4", "@babel/preset-env": "^7.27.2", "@markedjs/eslint-config": "^1.0.12", "@semantic-release/changelog": "^6.0.3", "@semantic-release/commit-analyzer": "^13.0.1", "@semantic-release/git": "^10.0.1", "@semantic-release/github": "^11.0.3", "@semantic-release/npm": "^12.0.1", "@semantic-release/release-notes-generator": "^14.0.3", "babel-jest": "^30.0.2", "eslint": "^9.29.0", "globals": "^16.2.0", "highlight.js": "^11.11.1", "jest-cli": "^30.0.2", "marked": "^16.0.0", "rollup": "^4.44.0", "semantic-release": "^24.2.5", "tsd": "^0.32.0"}, "tsd": {"compilerOptions": {"paths": {"marked-highlight": ["./src"]}}}}