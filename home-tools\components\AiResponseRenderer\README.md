# AiResponseRenderer 组件

一个强大的内容渲染组件，支持 Markdown、HTML 以及两者的混合内容。

## 功能特性

### 🎯 智能内容检测
- 自动检测内容类型（Markdown、HTML、混合）
- 根据内容类型选择最佳渲染策略

### 📝 Markdown 支持
- **标题**：支持 # ## ### 语法
- **文本格式**：粗体 `**text**`、斜体 `*text*`、删除线 `~~text~~`
- **列表**：有序列表和无序列表
- **引用块**：`> 引用内容`
- **代码**：内联代码 `` `code` `` 和代码块 ``` 
- **链接**：`[文本](URL)` 和自动链接识别
- **图片**：`![alt](src)` 语法
- **表格**：标准 Markdown 表格语法
- **分割线**：`---` 或 `***`

### 🌐 HTML 支持
- 完整的 HTML 标签支持
- 保持原有样式和属性
- 与 Markdown 无缝混合

### 😊 Emoji 处理
- 文本表情符号转换：`:)` → 😊
- 原生 Emoji 支持
- 特殊符号处理：`...` → …，`~` → ～

### 🎨 特殊内容
- **Mermaid 图表**：```mermaid 代码块
- **LaTeX 数学公式**：```latex 代码块
- **代码高亮**：支持多种编程语言

## 使用方法

### 基础用法

```vue
<template>
  <AiResponseRenderer :content="content" />
</template>

<script setup>
import AiResponseRenderer from './components/AiResponseRenderer/index.vue'

const content = ref(`
# 标题
这是 **粗体** 和 *斜体* 文本。
`)
</script>
```

### 属性配置

```vue
<AiResponseRenderer 
  :content="content"
  :fontSize="'lg'"
/>
```

#### Props

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| content | string | - | 要渲染的内容 |
| fontSize | 'sm' \| 'base' \| 'lg' | 'base' | 字体大小 |

## 内容类型示例

### 1. 纯 Markdown
```markdown
# 标题
这是一个 **Markdown** 文档。

- 列表项 1
- 列表项 2

[链接](https://example.com)
```

### 2. 纯 HTML
```html
<div class="custom-container">
  <h1>HTML 标题</h1>
  <p>这是 <strong>HTML</strong> 内容。</p>
</div>
```

### 3. 混合内容
```markdown
# Markdown 标题

这是 Markdown 段落。

<div style="background: #f0f8ff; padding: 10px;">
  <p>这是嵌入的 HTML 内容</p>
</div>

## 继续 Markdown

- Markdown 列表
```

## 样式定制

组件使用 Tailwind CSS 类名，可以通过以下方式定制样式：

### 1. 全局样式覆盖
```css
/* 覆盖标题样式 */
.ai-response-container :deep(h1) {
  color: #custom-color;
}
```

### 2. 响应式设计
组件内置响应式支持，在移动设备上自动调整：
- 字体大小
- 间距
- 表格布局

## 高级功能

### Mermaid 图表
```markdown
```mermaid
graph TD
    A[开始] --> B[处理]
    B --> C[结束]
```
```

### LaTeX 数学公式
```markdown
```latex
E = mc^2
```
```

### 代码高亮
```markdown
```javascript
function hello() {
  console.log('Hello World!');
}
```
```

## 性能优化

- 智能内容检测，避免不必要的处理
- 延迟加载特殊内容（Mermaid、LaTeX）
- CDN 库降级支持
- 错误边界处理

## 浏览器兼容性

- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 移动端浏览器
- 支持 ES6+ 语法

## 故障排除

### 常见问题

1. **内容不显示**
   - 检查 content 属性是否正确传递
   - 查看浏览器控制台是否有错误

2. **样式异常**
   - 确保 Tailwind CSS 已正确加载
   - 检查是否有样式冲突

3. **特殊内容不渲染**
   - 确保相关 CDN 库已加载（Mermaid、KaTeX）
   - 检查网络连接

### 调试模式

在开发环境中，组件会在控制台输出详细的错误信息，帮助定位问题。
